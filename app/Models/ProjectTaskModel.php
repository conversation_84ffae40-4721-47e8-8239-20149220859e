<?php

namespace App\Models;

use CodeIgniter\Model;

class ProjectTaskModel extends Model
{
    protected $table = 'project_tasks';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'project_id',
        'task_type_id',
        'task_name',
        'description',
        'assigned_to',
        'status',
        'priority',
        'depends_on',
        'estimated_hours',
        'target_days',
        'task_order',
        'is_locked',
        'auto_activated_at',
        'actual_hours',
        'start_date',
        'due_date',
        'completed_date',
        'notes',
        'created_by',
        'updated_by',
        // Task manager status fields
        'task_manager_status',
        'revision_count',
        'current_revision',
        'revision_requested_at',
        'revision_requested_by',
        'revision_notes',
        // Payment tracking fields
        'payment_amount',
        'payment_status',
        'payment_account'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    protected $validationRules = [
        'project_id' => 'required|integer',
        'task_type_id' => 'required|integer',
        'task_name' => 'required|max_length[150]',
        'assigned_to' => 'required|integer',
        'status' => 'in_list[not_started,in_progress,on_hold,completed]',
        'priority' => 'in_list[low,medium,high,urgent]',
        'target_days' => 'integer|greater_than[0]',
        'task_order' => 'permit_empty|integer|greater_than[0]',
        'created_by' => 'required|integer'
    ];

    protected $validationMessages = [
        'task_name' => [
            'required' => 'Task name is required',
            'max_length' => 'Task name cannot exceed 150 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get tasks for a specific project with related data
     */
    public function getProjectTasks($projectId, $includeCompleted = true)
    {
        $builder = $this->select('
            project_tasks.*,
            task_types.name as task_type_name,
            task_types.color as task_type_color,
            task_types.icon as task_type_icon,
            assigned_user.username as assigned_username,
            assigned_user.email as assigned_email,
            creator.username as created_by_username,
            depends_task.task_name as depends_on_task_name,
            project_tasks.current_revision,
            project_tasks.revision_count,
            project_tasks.task_manager_status
        ')
        ->join('task_types', 'task_types.id = project_tasks.task_type_id', 'left')
        ->join('users as assigned_user', 'assigned_user.id = project_tasks.assigned_to', 'left')
        ->join('users as creator', 'creator.id = project_tasks.created_by', 'left')
        ->join('project_tasks as depends_task', 'depends_task.id = project_tasks.depends_on', 'left')
        ->where('project_tasks.project_id', $projectId);

        if (!$includeCompleted) {
            $builder->where('project_tasks.status !=', 'completed');
        }

        return $builder->orderBy('task_types.sort_order', 'ASC')
                      ->orderBy('project_tasks.created_at', 'ASC')
                      ->findAll();
    }

    /**
     * Get task with full details
     */
    public function getTaskWithDetails($taskId)
    {
        return $this->select('
            project_tasks.*,
            task_types.name as task_type_name,
            task_types.color as task_type_color,
            task_types.icon as task_type_icon,
            task_types.description as task_type_description,
            assigned_user.username as assigned_username,
            assigned_user.email as assigned_email,
            creator.username as created_by_username,
            projects.project_name,
            projects.project_id as project_code,
            depends_task.task_name as depends_on_task_name
        ')
        ->join('task_types', 'task_types.id = project_tasks.task_type_id', 'left')
        ->join('users as assigned_user', 'assigned_user.id = project_tasks.assigned_to', 'left')
        ->join('users as creator', 'creator.id = project_tasks.created_by', 'left')
        ->join('projects', 'projects.id = project_tasks.project_id', 'left')
        ->join('project_tasks as depends_task', 'depends_task.id = project_tasks.depends_on', 'left')
        ->find($taskId);
    }

    /**
     * Get tasks assigned to a specific user
     */
    public function getUserTasks($userId, $status = null, $limit = null)
    {
        $builder = $this->select('
            project_tasks.*,
            task_types.name as task_type_name,
            task_types.color as task_type_color,
            task_types.icon as task_type_icon,
            projects.project_name,
            projects.project_id as project_code,
            projects.client_name
        ')
        ->join('task_types', 'task_types.id = project_tasks.task_type_id', 'left')
        ->join('projects', 'projects.id = project_tasks.project_id', 'left')
        ->where('project_tasks.assigned_to', $userId);

        if ($status) {
            if (is_array($status)) {
                $builder->whereIn('project_tasks.status', $status);
            } else {
                $builder->where('project_tasks.status', $status);
            }
        }

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->orderBy('project_tasks.due_date', 'ASC')
                      ->orderBy('project_tasks.priority', 'DESC')
                      ->findAll();
    }

    /**
     * Get overdue tasks
     */
    public function getOverdueTasks($userId = null)
    {
        $builder = $this->select('
            project_tasks.*,
            task_types.name as task_type_name,
            task_types.color as task_type_color,
            projects.project_name,
            projects.project_id as project_code,
            assigned_user.username as assigned_username
        ')
        ->join('task_types', 'task_types.id = project_tasks.task_type_id', 'left')
        ->join('projects', 'projects.id = project_tasks.project_id', 'left')
        ->join('users as assigned_user', 'assigned_user.id = project_tasks.assigned_to', 'left')
        ->where('project_tasks.due_date <', date('Y-m-d'))
        ->whereIn('project_tasks.status', ['not_started', 'in_progress']);

        if ($userId) {
            $builder->where('project_tasks.assigned_to', $userId);
        }

        return $builder->orderBy('project_tasks.due_date', 'ASC')
                      ->findAll();
    }

    /**
     * Update task status with automatic date tracking and dependency unlocking
     */
    public function updateTaskStatus($taskId, $newStatus, $notes = null, $updatedBy = null)
    {
        $updateData = [
            'status' => $newStatus,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($notes) {
            $updateData['notes'] = $notes;
        }

        if ($updatedBy) {
            $updateData['updated_by'] = $updatedBy;
        }

        // Set completion date if completed
        if ($newStatus === 'completed') {
            $updateData['completed_date'] = date('Y-m-d H:i:s');
        } else {
            $updateData['completed_date'] = null;
        }

        $result = $this->update($taskId, $updateData);

        // If task is completed, unlock dependent tasks
        if ($result && $newStatus === 'completed') {
            $this->unlockDependentTasks($taskId);
        }

        return $result;
    }

    /**
     * Update task status with file attachment support
     */
    public function updateTaskStatusWithAttachment($taskId, $newStatus, $notes = null, $updatedBy = null, $filePath = null)
    {
        $updateData = [
            'status' => $newStatus,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($notes) {
            $updateData['notes'] = $notes;
        }

        if ($updatedBy) {
            $updateData['updated_by'] = $updatedBy;
        }

        // Set completion date if completed
        if ($newStatus === 'completed') {
            $updateData['completed_date'] = date('Y-m-d H:i:s');
        } else {
            $updateData['completed_date'] = null;
        }

        // For now, we'll store the file path in notes if provided
        // In a full implementation, you might want to add an attachments table
        if ($filePath && $notes) {
            $updateData['notes'] = $notes . "\n[Attachment: " . basename($filePath) . "]";
        } elseif ($filePath) {
            $updateData['notes'] = "[Attachment: " . basename($filePath) . "]";
        }

        $result = $this->update($taskId, $updateData);

        // If task is completed, unlock dependent tasks
        if ($result && $newStatus === 'completed') {
            $this->unlockDependentTasks($taskId);
        }

        return $result;
    }

    /**
     * Create default tasks for a project
     */
    public function createDefaultTasksForProject($projectId, $assignedTo, $createdBy)
    {
        $taskTypeModel = new TaskTypeModel();
        $defaultTaskTypes = $taskTypeModel->getDefaultTaskTypes();

        $tasks = [];
        $previousTaskId = null;

        foreach ($defaultTaskTypes as $index => $taskType) {
            $task = [
                'project_id' => $projectId,
                'task_type_id' => $taskType['id'],
                'task_name' => $taskType['name'],
                'description' => $taskType['description'],
                'assigned_to' => $assignedTo,
                'status' => 'not_started',
                'priority' => 'medium',
                'depends_on' => $previousTaskId, // Each task depends on the previous one
                'created_by' => $createdBy
            ];

            $taskId = $this->insert($task);
            $tasks[] = $this->find($taskId);
            $previousTaskId = $taskId;
        }

        return $tasks;
    }

    /**
     * Check if task dependencies are met (requires completion AND manager approval)
     */
    public function canStartTask($taskId)
    {
        $task = $this->find($taskId);

        if (!$task || !$task['depends_on']) {
            return true; // No dependencies
        }

        $dependentTask = $this->find($task['depends_on']);

        if (!$dependentTask || $dependentTask['status'] !== 'completed') {
            return false; // Dependency not completed
        }

        // Get the project for the dependent task to check manager approval
        $projectModel = new \App\Models\ProjectModel();
        $dependentProject = $projectModel->find($dependentTask['project_id']);

        // Require manager approval (client_accepted) for dependency to be satisfied
        return $dependentProject && $dependentProject['manager_status'] === 'client_accepted';
    }

    /**
     * Get task statistics for a project (excluding deleted tasks)
     */
    public function getProjectTaskStats($projectId)
    {
        // Get detailed task statistics
        $totalTasks = $this->where('project_id', $projectId)->countAllResults(false);
        $notStarted = $this->where('project_id', $projectId)->where('status', 'not_started')->countAllResults(false);
        $inProgress = $this->where('project_id', $projectId)->where('status', 'in_progress')->countAllResults(false);
        $onHold = $this->where('project_id', $projectId)->where('status', 'on_hold')->countAllResults(false);
        $completed = $this->where('project_id', $projectId)->where('status', 'completed')->countAllResults(false);

        // Get hold reasons for tasks on hold
        $holdTasks = $this->select('task_name, notes, updated_at')
            ->where('project_id', $projectId)
            ->where('status', 'on_hold')
            ->findAll();

        $holdReasons = [];
        foreach ($holdTasks as $task) {
            $holdReasons[] = [
                'task_name' => $task['task_name'],
                'reason' => $task['notes'] ?: 'No reason specified',
                'date' => $task['updated_at']
            ];
        }

        $stats = [
            'total_tasks' => $totalTasks,
            'not_started' => $notStarted,
            'in_progress' => $inProgress,
            'on_hold' => $onHold,
            'completed' => $completed,
            'hold_reasons' => $holdReasons
        ];

        $stats['completion_percentage'] = $totalTasks > 0
            ? round(($completed / $totalTasks) * 100, 1)
            : 0;

        return $stats;
    }

    /**
     * Get task timeline for a project
     */
    public function getProjectTimeline($projectId)
    {
        return $this->select('
            project_tasks.*,
            task_types.name as task_type_name,
            task_types.color as task_type_color,
            task_types.icon as task_type_icon,
            assigned_user.username as assigned_username
        ')
        ->join('task_types', 'task_types.id = project_tasks.task_type_id', 'left')
        ->join('users as assigned_user', 'assigned_user.id = project_tasks.assigned_to', 'left')
        ->where('project_tasks.project_id', $projectId)
        ->orderBy('task_types.sort_order', 'ASC')
        ->findAll();
    }

    /**
     * Get user workload statistics (excluding deleted tasks)
     */
    public function getUserWorkload($userId)
    {
        // Use builder to ensure soft deletes are properly handled
        $builder = $this->builder();

        $stats = [
            'total_assigned' => $builder->where('assigned_to', $userId)->countAllResults(false),
            'not_started' => $builder->where('assigned_to', $userId)->where('status', 'not_started')->countAllResults(false),
            'in_progress' => $builder->where('assigned_to', $userId)->where('status', 'in_progress')->countAllResults(false),
            'on_hold' => $builder->where('assigned_to', $userId)->where('status', 'on_hold')->countAllResults(false),
            'completed' => $builder->where('assigned_to', $userId)->where('status', 'completed')->countAllResults(false),
            'overdue' => $builder->where('assigned_to', $userId)
                             ->where('due_date <', date('Y-m-d'))
                             ->whereIn('status', ['not_started', 'in_progress'])
                             ->countAllResults(false)
        ];

        return $stats;
    }

    /**
     * Get tasks for a project with dependency information
     */
    public function getProjectTasksWithDependencies($projectId)
    {
        return $this->select('project_tasks.*,
                             users.username as assigned_username,
                             users.first_name as assigned_first_name,
                             users.last_name as assigned_last_name,
                             dependency.task_name as dependency_task_name,
                             dependency.status as dependency_status')
                    ->join('users', 'users.id = project_tasks.assigned_to', 'left')
                    ->join('project_tasks as dependency', 'dependency.id = project_tasks.depends_on', 'left')
                    ->where('project_tasks.project_id', $projectId)
                    ->orderBy('project_tasks.task_order', 'ASC')
                    ->findAll();
    }

    /**
     * Get unlocked tasks for an assignee
     */
    public function getUnlockedTasksForAssignee($assigneeId)
    {
        return $this->select('project_tasks.*,
                             projects.project_name,
                             projects.client_name')
                    ->join('projects', 'projects.id = project_tasks.project_id', 'left')
                    ->where('project_tasks.assigned_to', $assigneeId)
                    ->where('project_tasks.is_locked', false)
                    ->whereIn('project_tasks.status', ['not_started', 'in_progress'])
                    ->orderBy('project_tasks.task_order', 'ASC')
                    ->findAll();
    }

    /**
     * Check if a task can be unlocked (dependency completed)
     */
    public function canUnlockTask($taskId)
    {
        $task = $this->find($taskId);
        if (!$task || !$task['depends_on']) {
            return true; // No dependency or task not found
        }

        $dependency = $this->find($task['depends_on']);
        return $dependency && $dependency['status'] === 'completed';
    }

    /**
     * Unlock dependent tasks when a task is completed
     */
    public function unlockDependentTasks($completedTaskId)
    {
        // Find all tasks that depend on this completed task
        $dependentTasks = $this->where('depends_on', $completedTaskId)
                              ->where('is_locked', true)
                              ->findAll();

        $unlockedCount = 0;
        foreach ($dependentTasks as $task) {
            if ($this->canUnlockTask($task['id'])) {
                $this->update($task['id'], [
                    'is_locked' => false,
                    'auto_activated_at' => date('Y-m-d H:i:s')
                ]);
                $unlockedCount++;
            }
        }

        return $unlockedCount;
    }

    /**
     * Calculate project duration based on task dependencies
     */
    public function calculateProjectDuration($projectId)
    {
        $tasks = $this->where('project_id', $projectId)->findAll();

        if (empty($tasks)) {
            return 0;
        }

        // Build dependency graph
        $taskMap = [];
        $dependencyGraph = [];

        foreach ($tasks as $task) {
            $taskMap[$task['id']] = $task;
            $dependencyGraph[$task['id']] = [];
        }

        // Build the graph
        foreach ($tasks as $task) {
            if ($task['depends_on']) {
                $dependencyGraph[$task['depends_on']][] = $task['id'];
            }
        }

        // Calculate critical path
        $maxDuration = 0;
        $visited = [];

        foreach ($tasks as $task) {
            if (!$task['depends_on']) { // Start from root tasks
                $duration = $this->calculateTaskPath($task['id'], $taskMap, $dependencyGraph, $visited);
                $maxDuration = max($maxDuration, $duration);
            }
        }

        return $maxDuration;
    }

    /**
     * Calculate the longest path from a task (recursive)
     */
    private function calculateTaskPath($taskId, $taskMap, $dependencyGraph, &$visited)
    {
        if (isset($visited[$taskId])) {
            return $visited[$taskId];
        }

        $task = $taskMap[$taskId];
        $maxChildDuration = 0;

        // Calculate max duration of dependent tasks
        if (isset($dependencyGraph[$taskId])) {
            foreach ($dependencyGraph[$taskId] as $dependentTaskId) {
                $childDuration = $this->calculateTaskPath($dependentTaskId, $taskMap, $dependencyGraph, $visited);
                $maxChildDuration = max($maxChildDuration, $childDuration);
            }
        }

        $totalDuration = $task['target_days'] + $maxChildDuration;
        $visited[$taskId] = $totalDuration;

        return $totalDuration;
    }

    /**
     * Lock tasks based on dependencies (requires completion AND manager approval)
     */
    public function lockTasksBasedOnDependencies($projectId)
    {
        $tasks = $this->where('project_id', $projectId)->findAll();
        $projectModel = new \App\Models\ProjectModel();

        foreach ($tasks as $task) {
            if ($task['depends_on']) {
                $dependency = $this->find($task['depends_on']);
                $shouldLock = true; // Default to locked

                if ($dependency && $dependency['status'] === 'completed') {
                    // Check manager approval for the dependency project
                    $dependentProject = $projectModel->find($dependency['project_id']);
                    if ($dependentProject && $dependentProject['manager_status'] === 'client_accepted') {
                        $shouldLock = false; // Unlock if dependency is completed AND approved
                    }
                }

                $this->update($task['id'], [
                    'is_locked' => $shouldLock
                ]);

                log_message('info', "Task {$task['id']} ({$task['task_name']}) lock status: " . ($shouldLock ? 'LOCKED' : 'UNLOCKED'));
            } else {
                // Root tasks are never locked
                $this->update($task['id'], [
                    'is_locked' => false
                ]);
            }
        }
    }

    /**
     * Get task dependency chain
     */
    public function getTaskDependencyChain($taskId)
    {
        $chain = [];
        $currentTaskId = $taskId;

        while ($currentTaskId) {
            $task = $this->find($currentTaskId);
            if (!$task) break;

            $chain[] = $task;
            $currentTaskId = $task['depends_on'];
        }

        return array_reverse($chain); // Return from root to current task
    }

    /**
     * Increment revision number when task is sent back for revision
     */
    public function incrementRevision($taskId, $requestedBy, $notes = null)
    {
        $task = $this->find($taskId);
        if (!$task) {
            return false;
        }

        $newRevisionCount = ($task['revision_count'] ?? 0) + 1;
        $newCurrentRevision = ($task['current_revision'] ?? 0) + 1;

        $updateData = [
            'revision_count' => $newRevisionCount,
            'current_revision' => $newCurrentRevision,
            'revision_requested_at' => date('Y-m-d H:i:s'),
            'revision_requested_by' => $requestedBy,
            'revision_notes' => $notes,
            'status' => 'in_progress', // Reset to in_progress for revision
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->update($taskId, $updateData);
    }
}
