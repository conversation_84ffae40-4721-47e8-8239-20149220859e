<?php

namespace App\Models;

use CodeIgniter\Model;

class ProjectModel extends Model
{
    protected $table = 'projects';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'project_id',
        'client_mobile',
        'project_name',
        'location',
        'description',
        'start_date',
        'target_completion',
        'estimated_completion',
        'actual_completion',
        'status',
        'progress_percentage',
        'last_update_notes',
        'last_updated',
        'created_by',
        'assigned_to',
        'assigned_manager',
        'assigned_supervisor',
        'client_access_token',
        'client_access_expires',
        // Manager status fields
        'manager_status',
        'manager_notes',
        'manager_reviewed_by',
        'manager_reviewed_at',
        // Revision tracking fields
        'revision_count',
        'current_revision',
        'revision_requested_at',
        'revision_requested_by',
        'revision_notes',
        // Project duration fields
        'total_estimated_days',
        'calculated_duration'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation rules
    protected $validationRules = [
        'project_name' => 'required|max_length[150]',
        'location' => 'permit_empty|max_length[255]',
        'start_date' => 'required|valid_date',
        'status' => 'required|in_list[not_started,planning,in_progress,on_hold,completed,cancelled,review,sent_for_review,revision_needed,client_accepted,task_completed]',
        'created_by' => 'required|integer'
    ];

    protected $validationMessages = [
        'project_name' => [
            'required' => 'Project name is required',
            'max_length' => 'Project name cannot exceed 150 characters'
        ],
        'location' => [
            'max_length' => 'Location cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;

    /**
     * Get projects for a specific user based on their role
     */
    public function getProjectsForUser($userId, $userRoles, $filter = 'assigned')
    {
        $builder = $this->select('projects.*,
                                 creator.username as created_by_username,
                                 assigned_user.username as assigned_username,
                                 manager.username as manager_username,
                                 supervisor.username as supervisor_username,
                                 pt.task_name,
                                 pt.status as task_status,
                                 pt.id as task_id,
                                 pt.assigned_to as task_assigned_to,
                                 pt.depends_on,
                                 pt.is_locked,
                                 pt.task_order,
                                 dependency_task.status as dependency_status,
                                 dependency_task.task_name as dependency_task_name,
                                 (SELECT COUNT(*) FROM project_tasks pt2 WHERE pt2.depends_on = pt.id) as has_dependent_tasks,
                                 task_assignee.username as task_assigned_username,
                                 task_assignee.email as task_assigned_email,
                                 (SELECT created_at FROM project_timeline
                                  WHERE project_id = projects.id
                                  AND new_status = "in_progress"
                                  ORDER BY created_at DESC LIMIT 1) as current_session_start,
                                 (SELECT JSON_ARRAYAGG(
                                    JSON_OBJECT(
                                        "duration_seconds", JSON_EXTRACT(metadata, "$.duration_seconds"),
                                        "duration_formatted", JSON_EXTRACT(metadata, "$.duration_formatted"),
                                        "timestamp", created_at,
                                        "action", title
                                    )
                                  ) FROM project_timeline
                                  WHERE project_id = projects.id
                                  AND JSON_EXTRACT(metadata, "$.duration_seconds") > 0) as timeline_sessions,
                                 COALESCE(task_stats.total_tasks, 0) as total_tasks,
                                 COALESCE(task_stats.completed_tasks, 0) as completed_tasks,
                                 COALESCE(task_stats.in_progress_tasks, 0) as in_progress_tasks,
                                 COALESCE(task_stats.completion_percentage, 0) as completion_percentage')
                        ->join('users as creator', 'creator.id = projects.created_by', 'left')
                        ->join('users as assigned_user', 'assigned_user.id = projects.assigned_to', 'left')
                        ->join('users as manager', 'manager.id = projects.assigned_manager', 'left')
                        ->join('users as supervisor', 'supervisor.id = projects.assigned_supervisor', 'left')
                        ->join('project_tasks as pt', 'pt.project_id = projects.id', 'left')
                        ->join('users as task_assignee', 'task_assignee.id = pt.assigned_to', 'left')
                        ->join('project_tasks as dependency_task', 'dependency_task.id = pt.depends_on', 'left')
                        ->join('(SELECT
                                    project_id,
                                    COUNT(*) as total_tasks,
                                    SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_tasks,
                                    SUM(CASE WHEN status = "in_progress" THEN 1 ELSE 0 END) as in_progress_tasks,
                                    ROUND((SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as completion_percentage
                                  FROM project_tasks
                                  GROUP BY project_id
                                 ) task_stats', 'task_stats.project_id = projects.id', 'left');

        // Apply filter based on user role and request
        if ($filter === 'assigned') {
            // For non-admin users, show only accessible tasks (respect dependencies but keep running projects)
            if (!in_array($userRoles, ['admin', 'manager'])) {
                // Show tasks assigned to this user
                $builder->where('pt.assigned_to', $userId);

                // Apply dependency filtering with manager approval requirement:
                // 1. Have no dependencies (can start immediately)
                // 2. Dependencies are completed AND approved by manager (can start)
                // 3. Already in progress or completed (preserve existing running projects)
                $builder->groupStart()
                       ->where('pt.depends_on IS NULL') // Tasks with no dependencies
                       ->orGroupStart()
                           ->where('pt.depends_on IS NOT NULL')
                           ->where('dependency_task.status', 'completed') // Dependencies completed
                           ->where('dependency_task.manager_status', 'client_accepted') // AND manager approved
                       ->groupEnd()
                       ->orWhere('pt.status', 'in_progress') // Keep existing running tasks
                       ->orWhere('pt.status', 'completed') // Keep completed tasks
                       ->orWhere('pt.status', 'on_hold') // Keep on-hold tasks
                       ->groupEnd();

                // Debug logging
                log_message('info', "Dependency filtering applied for user $userId with roles: " . print_r($userRoles, true));
            } else {
                // For admin/manager, show projects assigned to the user
                $builder->where('projects.assigned_to', $userId);
            }
        } elseif ($filter === 'created') {
            // Show only projects created by the user
            $builder->where('projects.created_by', $userId);
        } elseif ($filter === 'all') {
            // If not admin, show projects user is involved in with dependency filtering
            if (!in_array($userRoles, ['admin', 'manager'])) {
                $builder->groupStart()
                       ->where('projects.created_by', $userId)
                       // For project-level assignments with accessible tasks
                       ->orGroupStart()
                           ->groupStart()
                               ->where('projects.assigned_to', $userId)
                               ->orWhere('projects.assigned_manager', $userId)
                               ->orWhere('projects.assigned_supervisor', $userId)
                           ->groupEnd()
                           // AND user has accessible tasks (with manager approval requirement)
                           ->groupStart()
                               ->where('pt.assigned_to', $userId)
                               ->groupStart()
                                   ->where('pt.depends_on IS NULL') // No dependencies
                                   ->orGroupStart()
                                       ->where('pt.depends_on IS NOT NULL')
                                       ->where('dependency_task.status', 'completed') // Dependencies completed
                                       ->where('dependency_task.manager_status', 'client_accepted') // AND manager approved
                                   ->groupEnd()
                                   ->orWhere('pt.status', 'in_progress') // Keep running tasks
                                   ->orWhere('pt.status', 'completed') // Keep completed tasks
                                   ->orWhere('pt.status', 'on_hold') // Keep on-hold tasks
                               ->groupEnd()
                           ->groupEnd()
                       ->groupEnd()
                       // OR direct accessible task assignments (with manager approval requirement)
                       ->orGroupStart()
                           ->where('pt.assigned_to', $userId)
                           ->groupStart()
                               ->where('pt.depends_on IS NULL') // No dependencies
                               ->orGroupStart()
                                   ->where('pt.depends_on IS NOT NULL')
                                   ->where('dependency_task.status', 'completed') // Dependencies completed
                                   ->where('dependency_task.manager_status', 'client_accepted') // AND manager approved
                               ->groupEnd()
                               ->orWhere('pt.status', 'in_progress') // Keep running tasks
                               ->orWhere('pt.status', 'completed') // Keep completed tasks
                               ->orWhere('pt.status', 'on_hold') // Keep on-hold tasks
                           ->groupEnd()
                       ->groupEnd()
                       ->groupEnd();
            }
        }

        // Sort by target completion date (earliest first) for assigned projects
        if ($filter === 'assigned') {
            return $builder->orderBy('projects.target_completion', 'ASC')->findAll();
        }

        return $builder->orderBy('projects.created_at', 'DESC')->findAll();
    }

    /**
     * Get projects by assignee (for admin filtering)
     */
    public function getProjectsByAssignee($assigneeId)
    {
        return $this->select('projects.*,
                             creator.username as created_by_username,
                             assigned_user.username as assigned_username,
                             manager.username as manager_username,
                             supervisor.username as supervisor_username,
                             pt.task_name,
                             pt.status as task_status,
                             pt.id as task_id,
                             pt.assigned_to as task_assigned_to,
                             pt.depends_on,
                             pt.is_locked,
                             pt.task_order,
                             dependency_task.status as dependency_status,
                             dependency_task.task_name as dependency_task_name,
                             (SELECT COUNT(*) FROM project_tasks pt2 WHERE pt2.depends_on = pt.id) as has_dependent_tasks,
                             task_assignee.username as task_assigned_username,
                             task_assignee.email as task_assigned_email,
                             (SELECT created_at FROM project_timeline
                              WHERE project_id = projects.id
                              AND new_status = "in_progress"
                              ORDER BY created_at DESC LIMIT 1) as current_session_start,
                             (SELECT JSON_ARRAYAGG(
                                JSON_OBJECT(
                                    "duration_seconds", JSON_EXTRACT(metadata, "$.duration_seconds"),
                                    "duration_formatted", JSON_EXTRACT(metadata, "$.duration_formatted"),
                                    "timestamp", created_at,
                                    "action", title
                                )
                              ) FROM project_timeline
                              WHERE project_id = projects.id
                              AND JSON_EXTRACT(metadata, "$.duration_seconds") > 0) as timeline_sessions,
                             (SELECT created_at FROM project_timeline
                              WHERE project_id = projects.id
                              AND new_status = "in_progress"
                              ORDER BY created_at DESC LIMIT 1) as current_session_start,
                             COALESCE(task_stats.total_tasks, 0) as total_tasks,
                             COALESCE(task_stats.completed_tasks, 0) as completed_tasks,
                             COALESCE(task_stats.in_progress_tasks, 0) as in_progress_tasks,
                             COALESCE(task_stats.completion_percentage, 0) as completion_percentage')
                    ->join('users as creator', 'creator.id = projects.created_by', 'left')
                    ->join('users as assigned_user', 'assigned_user.id = projects.assigned_to', 'left')
                    ->join('users as manager', 'manager.id = projects.assigned_manager', 'left')
                    ->join('users as supervisor', 'supervisor.id = projects.assigned_supervisor', 'left')
                    ->join('project_tasks as pt', 'pt.project_id = projects.id', 'left')
                    ->join('users as task_assignee', 'task_assignee.id = pt.assigned_to', 'left')
                    ->join('project_tasks as dependency_task', 'dependency_task.id = pt.depends_on', 'left')
                    ->join('(SELECT
                                project_id,
                                COUNT(*) as total_tasks,
                                SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_tasks,
                                SUM(CASE WHEN status = "in_progress" THEN 1 ELSE 0 END) as in_progress_tasks,
                                ROUND((SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as completion_percentage
                              FROM project_tasks
                              GROUP BY project_id
                             ) task_stats', 'task_stats.project_id = projects.id', 'left')
                    ->where('projects.assigned_to', $assigneeId)
                    ->orderBy('projects.target_completion', 'ASC')
                    ->findAll();
    }

    /**
     * Get project with detailed information including time tracking
     */
    public function getProjectWithDetails($projectId)
    {
        $project = $this->select('projects.*,
                                 assigned_user.name as assigned_username,
                                 assigned_user.email as assigned_email')
                        ->join('users as assigned_user', 'assigned_user.id = projects.user_id', 'left')
                        ->find($projectId);

        if ($project) {
            // Calculate time spent in each status
            $project['time_tracking'] = $this->calculateTimeInStatuses($project);
        }

        return $project;
    }

    /**
     * Calculate time spent in each project status
     */
    private function calculateTimeInStatuses($project)
    {
        $timeTracking = [
            'not_started' => 0,
            'in_progress' => 0,
            'on_hold' => 0,
            'completed' => 0,
            'total_time' => 0
        ];

        $startTime = strtotime($project['created_at']);
        $currentTime = time();

        // Calculate time in each status based on timestamps
        if ($project['not_started_at']) {
            $notStartedTime = strtotime($project['not_started_at']);
            $timeTracking['not_started'] = $notStartedTime - $startTime;
            $lastTime = $notStartedTime;
        } else {
            $lastTime = $startTime;
        }

        if ($project['in_progress_at']) {
            $inProgressTime = strtotime($project['in_progress_at']);
            $timeTracking['in_progress'] = $inProgressTime - $lastTime;
            $lastTime = $inProgressTime;
        }

        if ($project['on_hold_at']) {
            $onHoldTime = strtotime($project['on_hold_at']);
            $timeTracking['on_hold'] = $onHoldTime - $lastTime;
            $lastTime = $onHoldTime;
        }

        if ($project['completed_at']) {
            $completedTime = strtotime($project['completed_at']);
            $timeTracking['completed'] = $completedTime - $lastTime;
            $timeTracking['total_time'] = $completedTime - $startTime;
        } else {
            // Project is still active, calculate time up to now
            $timeTracking['total_time'] = $currentTime - $startTime;

            // Add current status time
            if ($project['status'] === 'in_progress' && $project['in_progress_at']) {
                $timeTracking['in_progress'] += $currentTime - strtotime($project['in_progress_at']);
            } elseif ($project['status'] === 'on_hold' && $project['on_hold_at']) {
                $timeTracking['on_hold'] += $currentTime - strtotime($project['on_hold_at']);
            }
        }

        // Convert seconds to human readable format
        foreach ($timeTracking as $key => $seconds) {
            $timeTracking[$key . '_formatted'] = $this->formatDuration($seconds);
        }

        return $timeTracking;
    }

    /**
     * Format duration in seconds to human readable format
     */
    private function formatDuration($seconds)
    {
        if ($seconds < 60) {
            return $seconds . ' seconds';
        } elseif ($seconds < 3600) {
            return round($seconds / 60) . ' minutes';
        } elseif ($seconds < 86400) {
            return round($seconds / 3600, 1) . ' hours';
        } else {
            return round($seconds / 86400, 1) . ' days';
        }
    }

    /**
     * Get project statistics
     */
    public function getProjectStats($userId, $userRoles)
    {
        $builder = $this->builder();

        // If not admin, only count projects user is involved in
        if (strpos($userRoles, 'admin') === false) {
            $builder->groupStart()
                   ->where('created_by', $userId)
                   ->orWhere('assigned_manager', $userId)
                   ->orWhere('assigned_supervisor', $userId)
                   ->groupEnd();
        }

        $stats = [
            'total_projects' => $builder->countAllResults(false),
            'active_projects' => $builder->where('status', 'in_progress')->countAllResults(false),
            'completed_projects' => $builder->where('status', 'completed')->countAllResults(false),
            'on_hold_projects' => $builder->where('status', 'on_hold')->countAllResults(false)
        ];

        // Get projects by status
        $statusCounts = $this->select('status, COUNT(*) as count')
                            ->groupBy('status');

        if (strpos($userRoles, 'admin') === false) {
            $statusCounts->groupStart()
                        ->where('created_by', $userId)
                        ->orWhere('assigned_manager', $userId)
                        ->orWhere('assigned_supervisor', $userId)
                        ->groupEnd();
        }

        $stats['by_status'] = $statusCounts->find();

        // Calculate average progress
        $avgProgress = $this->selectAvg('progress_percentage', 'avg_progress');
        
        if (strpos($userRoles, 'admin') === false) {
            $avgProgress->groupStart()
                       ->where('created_by', $userId)
                       ->orWhere('assigned_manager', $userId)
                       ->orWhere('assigned_supervisor', $userId)
                       ->groupEnd();
        }

        $progressResult = $avgProgress->first();
        $stats['average_progress'] = round($progressResult['avg_progress'] ?? 0, 1);

        return $stats;
    }

    /**
     * Get projects by status
     */
    public function getProjectsByStatus($status, $userId = null, $userRoles = null)
    {
        $builder = $this->where('status', $status);

        if ($userId && strpos($userRoles, 'admin') === false) {
            $builder->groupStart()
                   ->where('created_by', $userId)
                   ->orWhere('assigned_manager', $userId)
                   ->orWhere('assigned_supervisor', $userId)
                   ->groupEnd();
        }

        return $builder->orderBy('created_at', 'DESC')->find();
    }

    /**
     * Get overdue projects
     */
    public function getOverdueProjects($userId = null, $userRoles = null)
    {
        $builder = $this->where('estimated_completion <', date('Y-m-d'))
                        ->whereIn('status', ['planning', 'in_progress']);

        if ($userId && strpos($userRoles, 'admin') === false) {
            $builder->groupStart()
                   ->where('created_by', $userId)
                   ->orWhere('assigned_manager', $userId)
                   ->orWhere('assigned_supervisor', $userId)
                   ->groupEnd();
        }

        return $builder->orderBy('estimated_completion', 'ASC')->find();
    }

    /**
     * Get recent project updates
     */
    public function getRecentUpdates($limit = 10, $userId = null, $userRoles = null)
    {
        $builder = $this->select('projects.*, users.username as updated_by_username')
                        ->join('users', 'users.id = projects.created_by', 'left')
                        ->where('projects.last_updated IS NOT NULL');

        if ($userId && strpos($userRoles, 'admin') === false) {
            $builder->groupStart()
                   ->where('projects.created_by', $userId)
                   ->orWhere('projects.assigned_manager', $userId)
                   ->orWhere('projects.assigned_supervisor', $userId)
                   ->groupEnd();
        }

        return $builder->orderBy('projects.last_updated', 'DESC')
                      ->limit($limit)
                      ->find();
    }

    /**
     * Search projects
     */
    public function searchProjects($query, $userId = null, $userRoles = null, $limit = 20, $offset = 0)
    {
        $builder = $this->select('projects.*, 
                                 creator.username as created_by_username,
                                 manager.username as manager_username,
                                 supervisor.username as supervisor_username')
                        ->join('users as creator', 'creator.id = projects.created_by', 'left')
                        ->join('users as manager', 'manager.id = projects.assigned_manager', 'left')
                        ->join('users as supervisor', 'supervisor.id = projects.assigned_supervisor', 'left')
                        ->groupStart()
                        ->like('projects.project_name', $query)
                        ->orLike('projects.location', $query)
                        ->orLike('projects.description', $query)
                        ->groupEnd();

        if ($userId && strpos($userRoles, 'admin') === false) {
            $builder->groupStart()
                   ->where('projects.created_by', $userId)
                   ->orWhere('projects.assigned_manager', $userId)
                   ->orWhere('projects.assigned_supervisor', $userId)
                   ->groupEnd();
        }

        return $builder->orderBy('projects.created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->find();
    }



    /**
     * Update project progress with history
     */
    public function updateProjectProgress($projectId, $progress, $notes = null, $updatedBy = null)
    {
        $updateData = [
            'progress_percentage' => $progress,
            'last_updated' => date('Y-m-d H:i:s')
        ];

        if ($notes) {
            $updateData['last_update_notes'] = $notes;
        }

        // Update the project
        $result = $this->update($projectId, $updateData);

        // TODO: Add to project history table if needed
        // $this->addProgressHistory($projectId, $progress, $notes, $updatedBy);

        return $result;
    }

    /**
     * Get project completion percentage by month
     */
    public function getCompletionTrend($months = 12, $userId = null, $userRoles = null)
    {
        $builder = $this->select("DATE_FORMAT(actual_completion, '%Y-%m') as month, COUNT(*) as completed")
                        ->where('status', 'completed')
                        ->where('actual_completion >=', date('Y-m-d', strtotime("-{$months} months")));

        if ($userId && strpos($userRoles, 'admin') === false) {
            $builder->groupStart()
                   ->where('created_by', $userId)
                   ->orWhere('assigned_manager', $userId)
                   ->orWhere('assigned_supervisor', $userId)
                   ->groupEnd();
        }

        return $builder->groupBy("DATE_FORMAT(actual_completion, '%Y-%m')")
                      ->orderBy('month', 'ASC')
                      ->find();
    }

    /**
     * Get budget vs actual cost analysis
     */
    public function getBudgetAnalysis($userId = null, $userRoles = null)
    {
        $builder = $this->select('
            COUNT(*) as total_projects,
            SUM(budget) as total_budget,
            SUM(actual_cost) as total_actual_cost,
            AVG(budget) as avg_budget,
            AVG(actual_cost) as avg_actual_cost
        ')->where('budget IS NOT NULL');

        if ($userId && strpos($userRoles, 'admin') === false) {
            $builder->groupStart()
                   ->where('created_by', $userId)
                   ->orWhere('assigned_manager', $userId)
                   ->orWhere('assigned_supervisor', $userId)
                   ->groupEnd();
        }

        return $builder->first();
    }

    /**
     * Get project timeline with status history
     */
    public function getProjectTimeline($projectId)
    {
        // Get project details
        $project = $this->find($projectId);
        if (!$project) {
            return [];
        }

        $timeline = [];
        $currentTime = time();

        // Project creation
        $timeline[] = [
            'status' => 'created',
            'created_at' => $project['created_at'],
            'username' => 'System',
            'notes' => 'Project created',
            'duration' => null
        ];

        // Status transitions based on timestamp fields
        $statusFields = [
            'planning_at' => 'planning',
            'in_progress_at' => 'in_progress',
            'on_hold_at' => 'on_hold',
            'review_at' => 'review',
            'completed_at' => 'completed'
        ];

        $lastTimestamp = strtotime($project['created_at']);

        foreach ($statusFields as $field => $status) {
            if (!empty($project[$field])) {
                $statusTime = strtotime($project[$field]);
                $duration = $this->formatDuration($statusTime - $lastTimestamp);

                $timeline[] = [
                    'status' => $status,
                    'created_at' => $project[$field],
                    'username' => $project['assigned_username'] ?? 'Unknown User',
                    'notes' => $project['last_update_notes'] ?? 'Status changed to ' . ucwords(str_replace('_', ' ', $status)),
                    'duration' => $duration
                ];

                $lastTimestamp = $statusTime;
            }
        }

        // If project is not completed, add current duration
        if ($project['status'] !== 'completed') {
            $currentDuration = $this->formatDuration($currentTime - $lastTimestamp);
            $timeline[count($timeline) - 1]['duration'] = $currentDuration . ' (ongoing)';
        }

        return $timeline;
    }

    /**
     * Get sample projects for demo
     */
    public function getSampleProjects()
    {
        return [
            [
                'id' => 1,
                'project_name' => 'Residential Complex - Phase 1',
                'location' => 'Downtown District, City Center',
                'description' => '50-unit residential complex with modern amenities',
                'start_date' => '2024-01-15',
                'estimated_completion' => '2024-12-31',
                'budget' => 2500000.00,
                'actual_cost' => 1800000.00,
                'status' => 'in_progress',
                'progress_percentage' => 65,
                'last_update_notes' => 'Foundation work completed, starting structural work',
                'created_by_username' => 'Project Manager',
                'created_at' => '2024-01-10 09:00:00'
            ],
            [
                'id' => 2,
                'client_name' => 'XYZ Developers',
                'project_name' => 'Commercial Plaza',
                'location' => 'Business District, Main Street',
                'description' => 'Multi-story commercial plaza with retail and office spaces',
                'start_date' => '2024-03-01',
                'estimated_completion' => '2025-06-30',
                'budget' => 5000000.00,
                'actual_cost' => 1200000.00,
                'status' => 'in_progress',
                'progress_percentage' => 25,
                'last_update_notes' => 'Site preparation completed, excavation in progress',
                'created_by_username' => 'Site Manager',
                'created_at' => '2024-02-25 14:30:00'
            ],
            [
                'id' => 3,
                'client_name' => 'Green Homes Inc',
                'project_name' => 'Eco-Friendly Villas',
                'location' => 'Suburban Area, Green Valley',
                'description' => 'Sustainable housing project with solar panels and rainwater harvesting',
                'start_date' => '2023-09-01',
                'estimated_completion' => '2024-08-31',
                'budget' => 3200000.00,
                'actual_cost' => 3100000.00,
                'status' => 'completed',
                'progress_percentage' => 100,
                'last_update_notes' => 'Project completed successfully, handover done',
                'created_by_username' => 'Project Director',
                'created_at' => '2023-08-20 11:15:00'
            ]
        ];
    }
}
