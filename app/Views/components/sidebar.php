<?php
$authLib = new \App\Libraries\AuthLibrary();
$user = $user ?? $authLib->user();
?>

<!-- Sidebar -->
<nav class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <a href="/dashboard" class="logo">
            <div class="logo-icon">
                <i class="fas fa-bolt"></i>
            </div>
            <div class="logo-text">SmartFlo</div>
        </a>
    </div>

    <div class="sidebar-nav">
        <!-- Main Navigation -->
        <div class="nav-section">
            <div class="nav-item">
                <a href="/dashboard" class="nav-link <?= (uri_string() === 'dashboard' || uri_string() === '') ? 'active' : '' ?>" data-tooltip="Dashboard">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/auth/profile" class="nav-link <?= strpos(uri_string(), 'auth/profile') === 0 ? 'active' : '' ?>" data-tooltip="Profile">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/messages" class="nav-link <?= strpos(uri_string(), 'messages') === 0 ? 'active' : '' ?>" data-tooltip="Messages">
                    <i class="fas fa-envelope"></i>
                    <span>Messages</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/notifications" class="nav-link <?= strpos(uri_string(), 'notifications') === 0 ? 'active' : '' ?>" data-tooltip="Notifications">
                    <i class="fas fa-bell"></i>
                    <span>Notifications</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/blob/manager" class="nav-link <?= strpos(uri_string(), 'blob/manager') === 0 ? 'active' : '' ?>" data-tooltip="File Manager">
                    <i class="fas fa-folder-open"></i>
                    <span>File Manager</span>
                </a>
            </div>
        </div>

        <!-- Project Management Section -->
        <?php if ($user): ?>
        <div class="nav-section">
            <div class="nav-section-title">Project Management</div>
            <div class="nav-item">
                <a href="/projects" class="nav-link <?= strpos(uri_string(), 'projects') === 0 ? 'active' : '' ?>" data-tooltip="Manage Projects">
                    <i class="fas fa-tasks"></i>
                    <span>Manage Projects</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/projects/status" class="nav-link <?= strpos(uri_string(), 'projects/status') === 0 ? 'active' : '' ?>" data-tooltip="Project Status">
                    <i class="fas fa-chart-line"></i>
                    <span>Project Status</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/projects/reports" class="nav-link <?= strpos(uri_string(), 'projects/reports') === 0 ? 'active' : '' ?>" data-tooltip="Project Reports">
                    <i class="fas fa-chart-bar"></i>
                    <span>Project Reports</span>
                </a>
            </div>
            <?php if ($user && (strpos($user['roles'] ?? '', 'admin') !== false || strpos($user['roles'] ?? '', 'manager') !== false)): ?>
            <div class="nav-item">
                <a href="/task-manager" class="nav-link <?= strpos(uri_string(), 'task-manager') === 0 ? 'active' : '' ?>" data-tooltip="Task Manager">
                    <i class="fas fa-cogs"></i>
                    <span>Task Manager</span>
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item">
                <a href="/reports" class="nav-link <?= strpos(uri_string(), 'reports') === 0 ? 'active' : '' ?>" data-tooltip="Daily Reports">
                    <i class="fas fa-file-chart-line"></i>
                    <span>Daily Reports</span>
                </a>
            </div>
        </div>
        <?php endif; ?>

        <!-- Regular Navigation Continues -->
        <div class="nav-section">
            <div class="nav-item">
                <a href="/settings" class="nav-link <?= strpos(uri_string(), 'settings') === 0 ? 'active' : '' ?>" data-tooltip="Settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
        </div>

        <!-- Administration Section -->
        <?php if ($user && (strpos($user['roles'] ?? '', 'admin') !== false || strpos($user['permissions'] ?? '', 'admin.access') !== false)): ?>
        <div class="nav-section">
            <div class="nav-section-title">Administration</div>
            <div class="nav-item">
                <a href="/admin/panel" class="nav-link <?= strpos(uri_string(), 'admin/panel') === 0 ? 'active' : '' ?>" data-tooltip="Admin Panel">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Admin Panel</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/admin/users" class="nav-link <?= strpos(uri_string(), 'admin/users') === 0 ? 'active' : '' ?>" data-tooltip="User Management">
                    <i class="fas fa-users"></i>
                    <span>User Management</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/admin/roles" class="nav-link <?= strpos(uri_string(), 'admin/roles') === 0 ? 'active' : '' ?>" data-tooltip="Role Management">
                    <i class="fas fa-user-shield"></i>
                    <span>Role Management</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/admin/security/failed-logins" class="nav-link <?= strpos(uri_string(), 'admin/security') === 0 ? 'active' : '' ?>" data-tooltip="Security Logs">
                    <i class="fas fa-shield-alt"></i>
                    <span>Security Logs</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/dashboard-controls" class="nav-link <?= strpos(uri_string(), 'dashboard-controls') === 0 ? 'active' : '' ?>" data-tooltip="Dashboard Controls">
                    <i class="fas fa-th-large"></i>
                    <span>Dashboard Controls</span>
                </a>
            </div>
        </div>
        <?php endif; ?>

        <!-- Account Section -->
        <div class="nav-section">
            <div class="nav-section-title">Account</div>
            <div class="nav-item">
                <a href="/auth/logout" class="nav-link" data-tooltip="Logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </div>
    </div>
</nav>
