<?= $this->extend('layouts/app') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Page Header -->
            <div class="page-header-create">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <h1 class="page-title mb-0">
                            <i class="fas fa-edit me-3"></i>
                            Edit Project: <?= esc($project['project_name']) ?>
                        </h1>
                    </div>
                    <div>
                        <a href="/projects" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Projects
                        </a>
                        <a href="/projects/view/<?= $project['id'] ?>" class="btn btn-outline-primary">
                            <i class="fas fa-eye me-2"></i>View Details
                        </a>
                    </div>
                </div>
            </div>

            <!-- Project Edit Form -->
            <div class="creation-form-container">
                <?php if (session('errors')): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach (session('errors') as $error): ?>
                        <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <?php if (session('error')): ?>
                <div class="alert alert-danger">
                    <?= session('error') ?>
                </div>
                <?php endif; ?>

                <form id="editProjectForm" method="POST" action="/projects/update/<?= $project['id'] ?>">
                    <?= csrf_field() ?>

                    <!-- Progress Steps -->
                    <div class="steps-container">
                        <div class="step-item active" data-step="1">
                            <div class="step-content">
                                <span class="step-number">1</span>
                                <span class="step-label">Project Details</span>
                            </div>
                        </div>
                        <div class="step-line"></div>
                        <div class="step-item" data-step="2">
                            <div class="step-content">
                                <span class="step-number">2</span>
                                <span class="step-label">Task Management</span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Project Details -->
                    <div class="form-section active" data-step="1">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="project_id" class="form-label">Project ID</label>
                                    <input type="text" class="form-control" id="project_id"
                                           value="<?= esc($project['project_id']) ?>" readonly>
                                    <small class="text-muted">Project ID cannot be changed</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="project_name" class="form-label required">Project Name</label>
                                    <input type="text" class="form-control" id="project_name" name="project_name"
                                           value="<?= esc(old('project_name', $project['project_name'])) ?>" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="start_date" class="form-label required">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date"
                                           value="<?= esc(old('start_date', $project['start_date'])) ?>" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="client_mobile" class="form-label">Client Mobile</label>
                                    <input type="tel" class="form-control" id="client_mobile" name="client_mobile"
                                           value="<?= esc(old('client_mobile', $project['client_mobile'])) ?>"
                                           placeholder="+91 98765 43210">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location" class="form-label">Project Location</label>
                                    <input type="text" class="form-control" id="location" name="location"
                                           value="<?= esc(old('location', $project['location'])) ?>"
                                           placeholder="https://maps.google.com/maps?q=location">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description" class="form-label">Project Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="Detailed project description..."><?= esc(old('description', $project['description'])) ?></textarea>
                        </div>
                    </div>

                    <!-- Step 2: Task Management -->
                    <div class="form-section" data-step="2">
                        <div id="existing-tasks">
                            <!-- Existing tasks will be loaded here -->
                        </div>

                        <div class="text-center mb-3">
                            <button type="button" class="btn btn-outline-primary" id="add-new-task">
                                <i class="fas fa-plus me-2"></i>
                                Add New Task
                            </button>
                        </div>
                    </div>

                    <!-- Form Navigation -->
                    <div class="form-navigation">
                        <button type="button" class="btn btn-secondary" id="prev-btn" style="display: none;">
                            <i class="fas fa-arrow-left me-2"></i>
                            Previous
                        </button>
                        <div class="nav-spacer"></div>
                        <div class="created-info">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Created: <?= date('M d, Y H:i', strtotime($project['created_at'])) ?>
                            </small>
                        </div>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='/projects'">
                            Cancel
                        </button>
                        <button type="button" class="btn btn-primary" id="next-btn">
                            Next
                            <i class="fas fa-arrow-right ms-2"></i>
                        </button>
                        <button type="submit" class="btn btn-success" id="submit-btn" style="display: none;">
                            <span class="spinner-border spinner-border-sm me-2" style="display: none;"></span>
                            <i class="fas fa-save me-2"></i>
                            Update Project
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    const totalSteps = 2;

    // Initialize form
    initializeForm();
    loadExistingTasks();
    setupMobileFormatting();

    function initializeForm() {
        updateStepDisplay();

        // Navigation buttons
        document.getElementById('next-btn').addEventListener('click', nextStep);
        document.getElementById('prev-btn').addEventListener('click', prevStep);

        // Form validation
        const form = document.getElementById('editProjectForm');
        form.addEventListener('submit', handleFormSubmit);

        // Setup real-time validation
        setupValidation();
    }

    function setupValidation() {
        const form = document.getElementById('editProjectForm');
        const inputs = form.querySelectorAll('input, textarea, select');

        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });

            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    validateField(this);
                }
            });
        });
    }

    function validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';

        // Clear previous validation
        field.classList.remove('is-invalid', 'is-valid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) feedback.textContent = '';

        if (!value && field.required) {
            isValid = false;
            message = 'This field is required';
        } else if (field.name === 'client_mobile' && value && value.trim() !== '+91') {
            // Check for Indian format (+91 98765 43210) or general international format
            if (!/^\+91\s[0-9]{5}\s[0-9]{5}$/.test(value) && !/^[\+]?[0-9\s\-\(\)]{10,20}$/.test(value)) {
                isValid = false;
                message = 'Please enter a valid mobile number (e.g., +91 98765 43210)';
            }
        }

        if (!isValid) {
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = message;
        } else if (value) {
            field.classList.add('is-valid');
        }

        return isValid;
    }

    function setupMobileFormatting() {
        const mobileInput = document.getElementById('client_mobile');
        if (mobileInput) {
            // Set default prefix if empty
            if (!mobileInput.value) {
                mobileInput.value = '+91 ';
            }

            mobileInput.addEventListener('input', function(e) {
                let value = e.target.value;

                // Always ensure +91 prefix
                if (!value.startsWith('+91')) {
                    value = '+91 ' + value.replace(/^\+?91?\s*/, '');
                }

                // Format as +91 XXXXX XXXXX
                const numbers = value.replace(/[^\d]/g, '').substring(2); // Remove +91 and non-digits
                if (numbers.length > 0) {
                    const formatted = numbers.substring(0, 5) + (numbers.length > 5 ? ' ' + numbers.substring(5, 10) : '');
                    e.target.value = '+91 ' + formatted;
                } else {
                    e.target.value = '+91 ';
                }
            });

            mobileInput.addEventListener('keydown', function(e) {
                // Prevent deletion of +91 prefix
                if (e.target.selectionStart <= 4 && (e.key === 'Backspace' || e.key === 'Delete')) {
                    e.preventDefault();
                }
            });
        }
    }

    function updateStepDisplay() {
        // Update step indicators
        document.querySelectorAll('.step-item').forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.toggle('active', stepNumber === currentStep);
            step.classList.toggle('completed', stepNumber < currentStep);
        });

        // Update step lines
        document.querySelectorAll('.step-line').forEach((line, index) => {
            line.classList.toggle('completed', index + 1 < currentStep);
        });

        // Show/hide form sections
        document.querySelectorAll('.form-section').forEach((section, index) => {
            const stepNumber = index + 1;
            section.classList.toggle('active', stepNumber === currentStep);
        });

        // Update navigation buttons
        document.getElementById('prev-btn').style.display = currentStep > 1 ? 'block' : 'none';
        document.getElementById('next-btn').style.display = currentStep < totalSteps ? 'block' : 'none';
        document.getElementById('submit-btn').style.display = currentStep === totalSteps ? 'block' : 'none';
    }

    function nextStep() {
        if (currentStep < totalSteps) {
            currentStep++;
            updateStepDisplay();
        }
    }

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            updateStepDisplay();
        }
    }

    function loadExistingTasks() {
        // Load existing tasks for this project
        fetch(`/projects/getTasks/<?= $project['id'] ?>`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayExistingTasks(data.tasks);
                }
            })
            .catch(error => {
                console.error('Error loading tasks:', error);
            });
    }

    function displayExistingTasks(tasks) {
        const container = document.getElementById('existing-tasks');
        if (tasks.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No tasks found for this project.</p>';
            return;
        }

        container.innerHTML = tasks.map(task => `
            <div class="task-edit-card" data-task-id="${task.id}">
                <div class="task-header">
                    <h6 class="task-title">
                        <i class="fas fa-tasks me-2"></i>
                        ${task.task_name}
                    </h6>
                    <div class="task-actions">
                        <button type="button" class="btn btn-sm btn-outline-primary edit-task" data-task-id="${task.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger delete-task" data-task-id="${task.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="task-details">
                    <div class="row">
                        <div class="col-md-3">
                            <small class="text-muted">Assignee:</small><br>
                            <span>${task.assigned_username || 'Unassigned'}</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Status:</small><br>
                            <span class="badge badge-${task.status}">${task.status.replace('_', ' ')}</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Priority:</small><br>
                            <span>${task.priority || 'Medium'}</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Target Days:</small><br>
                            <span>${task.target_days || 'N/A'}</span>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    function handleFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const submitBtn = document.getElementById('submit-btn');
        const spinner = submitBtn.querySelector('.spinner-border');

        // Show loading state
        submitBtn.disabled = true;
        spinner.style.display = 'inline-block';

        fetch(e.target.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message and redirect
                alert('Project updated successfully!');
                window.location.href = '/projects';
            } else {
                alert('Error: ' + (data.message || 'Failed to update project'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the project');
        })
        .finally(() => {
            // Hide loading state
            submitBtn.disabled = false;
            spinner.style.display = 'none';
        });
    }
});
</script>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<!-- Include the same CSS from create project page -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">

<style>
/* Copy all styles from create project page */
.page-header-create {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.creation-form-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    padding: 2.5rem;
    margin-bottom: 2rem;
}

/* Steps styling */
.steps-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 3rem;
    padding: 0 2rem;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    border: 3px solid #e9ecef;
}

.step-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.3s ease;
}

.step-item.active .step-number {
    background: #007bff;
    color: white;
    border-color: #007bff;
    transform: scale(1.1);
}

.step-item.active .step-label {
    color: #007bff;
}

.step-item.completed .step-number {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.step-item.completed .step-label {
    color: #28a745;
}

.step-line {
    flex: 1;
    height: 3px;
    background: #e9ecef;
    margin: 0 1rem;
    position: relative;
    top: -25px;
    transition: all 0.3s ease;
}

.step-line.completed {
    background: #28a745;
}

/* Form sections */
.form-section {
    display: none;
}

.form-section.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: block;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background-color: #fff;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    outline: none;
}

.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.38 1.38 3.02-3.02.94.94L2.3 10.27z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.valid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #28a745;
}

/* Navigation */
.form-navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

.nav-spacer {
    flex: 1;
}

.created-info {
    margin: 0 1rem;
}

/* Buttons */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    border-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border-color: #28a745;
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    border-color: #1e7e34;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* Task edit cards */
.task-edit-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.task-edit-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.1);
}

.task-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
}

.task-title {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.task-details {
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.badge {
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: capitalize;
}

.badge-not_started {
    background-color: #6c757d;
    color: white;
}

.badge-in_progress {
    background-color: #007bff;
    color: white;
}

.badge-completed {
    background-color: #28a745;
    color: white;
}

.badge-on_hold {
    background-color: #ffc107;
    color: #212529;
}

/* Responsive design */
@media (max-width: 768px) {
    .creation-form-container {
        padding: 1.5rem;
        margin: 1rem;
    }

    .page-header-create {
        margin: 1rem;
        padding: 1.5rem;
    }

    .steps-container {
        padding: 0 1rem;
    }

    .step-label {
        font-size: 0.8rem;
    }

    .form-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-spacer {
        display: none;
    }
}
</style>
<?= $this->endSection() ?>
