<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Manager Approvals</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/projects">Projects</a></li>
                    <li class="breadcrumb-item active">Approvals</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="/projects" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Projects
            </a>
        </div>
    </div>

    <!-- Pending Approvals -->
    <?php if (!empty($pendingApprovals)): ?>
    <div class="row">
        <?php foreach ($pendingApprovals as $project): ?>
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-project-diagram me-2"></i>
                        <?= esc($project['project_name']) ?>
                    </h6>
                    <span class="badge bg-warning">
                        <i class="fas fa-clock me-1"></i>Pending Review
                    </span>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Project ID:</small>
                        <strong><?= esc($project['project_id']) ?></strong>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">Client:</small>
                        <div><?= esc($project['client_name']) ?></div>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">Assigned To:</small>
                        <div><?= esc($project['assigned_username'] ?? 'Unassigned') ?></div>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">Status:</small>
                        <span class="badge bg-<?= getProjectStatusColor($project['status']) ?>">
                            <?= ucwords(str_replace('_', ' ', $project['status'])) ?>
                        </span>
                    </div>
                    
                    <?php if (!empty($project['manager_status'])): ?>
                    <div class="mb-3">
                        <small class="text-muted">Manager Status:</small>
                        <span class="badge bg-<?= getManagerStatusColor($project['manager_status']) ?>">
                            <?= ucwords(str_replace('_', ' ', $project['manager_status'])) ?>
                        </span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <small class="text-muted">Last Updated:</small>
                        <div><?= date('M d, Y h:i A', strtotime($project['updated_at'])) ?></div>
                    </div>
                    
                    <!-- Tasks List -->
                    <?php if (!empty($project['tasks'])): ?>
                    <div class="mb-3">
                        <small class="text-muted">Tasks:</small>
                        <div class="mt-2">
                            <?php foreach ($project['tasks'] as $task): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="<?= $task['status'] === 'completed' ? 'text-decoration-line-through text-muted' : '' ?>">
                                    <?= esc($task['task_name']) ?>
                                </span>
                                <span class="badge bg-<?= getTaskStatusColor($task['status']) ?>">
                                    <?= ucwords(str_replace('_', ' ', $task['status'])) ?>
                                </span>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="card-footer">
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-warning" 
                                onclick="updateProjectManagerStatus(<?= $project['id'] ?>, 'need_revision')"
                                title="Need Revision">
                            <i class="fas fa-undo me-1"></i>Revision
                        </button>
                        <button class="btn btn-sm btn-outline-success" 
                                onclick="updateProjectManagerStatus(<?= $project['id'] ?>, 'client_accepted')"
                                title="Accept">
                            <i class="fas fa-check me-1"></i>Accept
                        </button>
                        <button class="btn btn-sm btn-outline-danger" 
                                onclick="updateProjectManagerStatus(<?= $project['id'] ?>, 'rejected')"
                                title="Reject">
                            <i class="fas fa-times me-1"></i>Reject
                        </button>
                        <a href="/projects/view/<?= $project['id'] ?>" 
                           class="btn btn-sm btn-outline-primary ms-auto">
                            <i class="fas fa-eye me-1"></i>View
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php else: ?>
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
            <h4>All Caught Up!</h4>
            <p class="text-muted">No projects are currently pending your approval.</p>
            <a href="/projects" class="btn btn-primary">
                <i class="fas fa-project-diagram me-2"></i>View All Projects
            </a>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php
function getProjectStatusColor($status) {
    switch ($status) {
        case 'not_started': return 'secondary';
        case 'planning': return 'info';
        case 'in_progress': return 'primary';
        case 'on_hold': return 'warning';
        case 'completed': return 'success';
        case 'review': return 'warning';
        default: return 'secondary';
    }
}

function getManagerStatusColor($status) {
    switch ($status) {
        case 'sent_for_review': return 'warning';
        case 'need_revision': return 'danger';
        case 'client_accepted': return 'success';
        case 'rejected': return 'danger';
        default: return 'secondary';
    }
}

function getTaskStatusColor($status) {
    switch ($status) {
        case 'not_started': return 'secondary';
        case 'in_progress': return 'primary';
        case 'on_hold': return 'warning';
        case 'completed': return 'success';
        default: return 'secondary';
    }
}
?>

<script>
function updateProjectManagerStatus(projectId, status) {
    const statusNames = {
        'need_revision': 'Need Revision',
        'client_accepted': 'Client Accepted',
        'rejected': 'Rejected'
    };
    
    const statusName = statusNames[status] || status;
    
    if (!confirm(`Are you sure you want to mark this project as "${statusName}"?`)) {
        return;
    }
    
    const formData = new FormData();
    formData.append('status', status);
    formData.append('notes', `Project marked as ${statusName} by manager`);
    
    fetch(`/projects/updateManagerStatus/${projectId}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || `Project ${statusName} successfully`);
            // Reload page to update the list
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert('error', data.message || `Failed to update project status`);
        }
    })
    .catch(error => {
        console.error('Error updating project status:', error);
        showAlert('error', 'Error updating project status');
    });
}

function showAlert(type, message) {
    // Simple alert implementation - can be enhanced with toast notifications
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Insert at top of container
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>
<?= $this->endSection() ?>
