<?php

namespace App\Controllers;

use CodeIgniter\Controller;

class DatabaseFix extends Controller
{
    public function addTaskManagerStatus()
    {
        $db = \Config\Database::connect();
        $forge = \Config\Database::forge();

        try {
            // Check if task_manager_status column exists
            if (!$db->fieldExists('task_manager_status', 'project_tasks')) {
                $fields = [
                    'task_manager_status' => [
                        'type' => 'ENUM',
                        'constraint' => ['not_reviewed', 'sent_for_review', 'need_revision', 'client_accepted', 'rejected'],
                        'default' => 'not_reviewed',
                        'comment' => 'Task-level manager review status'
                    ]
                ];

                $forge->addColumn('project_tasks', $fields);
                echo "task_manager_status column added successfully to project_tasks table.\n";
            } else {
                echo "task_manager_status column already exists in project_tasks table.\n";
            }

            // Check if revision columns exist
            if (!$db->fieldExists('current_revision', 'project_tasks')) {
                $revisionFields = [
                    'revision_count' => [
                        'type' => 'INT',
                        'constraint' => 11,
                        'default' => 0,
                        'comment' => 'Number of revisions for this task'
                    ],
                    'current_revision' => [
                        'type' => 'INT',
                        'constraint' => 11,
                        'default' => 0,
                        'comment' => 'Current revision number'
                    ],
                    'revision_requested_at' => [
                        'type' => 'DATETIME',
                        'null' => true,
                        'comment' => 'When revision was last requested'
                    ],
                    'revision_requested_by' => [
                        'type' => 'INT',
                        'constraint' => 11,
                        'null' => true,
                        'comment' => 'User who requested the revision'
                    ],
                    'revision_notes' => [
                        'type' => 'TEXT',
                        'null' => true,
                        'comment' => 'Notes about the revision request'
                    ],
                    'payment_amount' => [
                        'type' => 'DECIMAL',
                        'constraint' => '10,2',
                        'null' => true,
                        'comment' => 'Payment amount for this task'
                    ],
                    'payment_status' => [
                        'type' => 'ENUM',
                        'constraint' => ['unpaid', 'paid', 'partial'],
                        'default' => 'unpaid',
                        'comment' => 'Payment status for this task'
                    ],
                    'payment_account' => [
                        'type' => 'VARCHAR',
                        'constraint' => 255,
                        'null' => true,
                        'comment' => 'Account where payment was received'
                    ]
                ];

                $forge->addColumn('project_tasks', $revisionFields);
                echo "Revision columns added successfully to project_tasks table.\n";
            } else {
                echo "Revision columns already exist in project_tasks table.\n";
            }

            return "Database update completed successfully!";

        } catch (\Exception $e) {
            return "Error updating database: " . $e->getMessage();
        }
    }
}
